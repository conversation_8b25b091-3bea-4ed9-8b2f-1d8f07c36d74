package main

import (
	"log"

	"adc-account-backend/internal/config"
	"adc-account-backend/internal/database"
	"adc-account-backend/internal/models"

	"github.com/shopspring/decimal"
)

func main() {
	log.Println("Starting database seeding...")

	// Load configuration
	cfg := config.Load()

	// Initialize database
	db, err := database.Initialize(cfg.DatabaseURL)
	if err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}

	// Check if data already exists
	var merchantCount int64
	db.Model(&models.Merchant{}).Count(&merchantCount)
	if merchantCount > 0 {
		log.Println("Database already seeded, skipping...")
		return
	}

	// Helper functions
	stringPtr := func(s string) *string { return &s }

	// Create merchant
	merchant := models.Merchant{
		ID:                  "cma6ptpdn0000f0lx2mp3lhob",
		Name:                "Demo Company Inc",
		LegalName:           stringPtr("Demo Company Incorporated"),
		Address:             stringPtr("123 Business St, Suite 100, Business City, BC 12345"),
		Phone:               stringPtr("******-123-4567"),
		PrimaryContactEmail: stringPtr("<EMAIL>"),
		Website:             stringPtr("https://democompany.com"),
		TaxID:               stringPtr("12-3456789"),
		Currency:            "USD",
		FiscalYearStart:     stringPtr("1"),
		LogoURL:             stringPtr(""),
		StripeCustomerID:    stringPtr(""),
	}

	if err := db.Create(&merchant).Error; err != nil {
		log.Fatalf("Failed to create merchant: %v", err)
	}
	log.Printf("Created merchant: %s", merchant.Name)

	// Create customers
	customers := []models.Customer{
		{
			MerchantID:   "cma6ptpdn0000f0lx2mp3lhob",
			Name:         "ABC Corporation",
			Email:        stringPtr("<EMAIL>"),
			Phone:        stringPtr("******-111-2222"),
			Address:      stringPtr("789 Corporate Blvd, Corp City, CC 11111"),
			TaxID:        stringPtr("11-1111111"),
			PaymentTerms: stringPtr("Net 30"),
			CreditLimit:  decimal.NewFromFloat(50000.00),
			IsActive:     true,
		},
		{
			MerchantID:   "cma6ptpdn0000f0lx2mp3lhob",
			Name:         "XYZ Industries",
			Email:        stringPtr("<EMAIL>"),
			Phone:        stringPtr("******-222-3333"),
			Address:      stringPtr("321 Industrial Way, Ind City, IC 22222"),
			TaxID:        stringPtr("22-2222222"),
			PaymentTerms: stringPtr("Net 15"),
			CreditLimit:  decimal.NewFromFloat(75000.00),
			IsActive:     true,
		},
		{
			MerchantID:   "cma6ptpdn0000f0lx2mp3lhob",
			Name:         "Tech Solutions Ltd",
			Email:        stringPtr("<EMAIL>"),
			Phone:        stringPtr("******-333-4444"),
			Address:      stringPtr("654 Tech Park Dr, Tech City, TC 33333"),
			TaxID:        stringPtr("33-3333333"),
			PaymentTerms: stringPtr("Net 30"),
			CreditLimit:  decimal.NewFromFloat(60000.00),
			IsActive:     true,
		},
		{
			MerchantID:   "cma6ptpdn0000f0lx2mp3lhob",
			Name:         "Global Services Inc",
			Email:        stringPtr("<EMAIL>"),
			Phone:        stringPtr("******-444-5555"),
			Address:      stringPtr("987 Global Plaza, Global City, GC 44444"),
			TaxID:        stringPtr("44-4444444"),
			PaymentTerms: stringPtr("Net 45"),
			CreditLimit:  decimal.NewFromFloat(40000.00),
			IsActive:     true,
		},
		{
			MerchantID:   "cma6ptpdn0000f0lx2mp3lhob",
			Name:         "Innovation Partners",
			Email:        stringPtr("<EMAIL>"),
			Phone:        stringPtr("******-555-6666"),
			Address:      stringPtr("147 Innovation St, Innov City, IC 55555"),
			TaxID:        stringPtr("55-5555555"),
			PaymentTerms: stringPtr("Net 30"),
			CreditLimit:  decimal.NewFromFloat(35000.00),
			IsActive:     true,
		},
	}

	for _, customer := range customers {
		if err := db.Create(&customer).Error; err != nil {
			log.Fatalf("Failed to create customer %s: %v", customer.Name, err)
		}
		log.Printf("Created customer: %s", customer.Name)
	}

	// Create vendors
	vendors := []models.Vendor{
		{
			MerchantID:   "cma6ptpdn0000f0lx2mp3lhob",
			Name:         "Office Supplies Co",
			Email:        stringPtr("<EMAIL>"),
			Phone:        stringPtr("******-666-7777"),
			Address:      stringPtr("258 Supply St, Supply City, SC 66666"),
			TaxID:        stringPtr("66-6666666"),
			PaymentTerms: stringPtr("Net 30"),
			IsActive:     true,
		},
		{
			MerchantID:   "cma6ptpdn0000f0lx2mp3lhob",
			Name:         "Tech Equipment Ltd",
			Email:        stringPtr("<EMAIL>"),
			Phone:        stringPtr("******-777-8888"),
			Address:      stringPtr("369 Equipment Ave, Equip City, EC 77777"),
			TaxID:        stringPtr("77-7777777"),
			PaymentTerms: stringPtr("Net 15"),
			IsActive:     true,
		},
		{
			MerchantID:   "cma6ptpdn0000f0lx2mp3lhob",
			Name:         "Utilities Provider",
			Email:        stringPtr("<EMAIL>"),
			Phone:        stringPtr("******-888-9999"),
			Address:      stringPtr("741 Utility Rd, Util City, UC 88888"),
			TaxID:        stringPtr("88-8888888"),
			PaymentTerms: stringPtr("Net 10"),
			IsActive:     true,
		},
		{
			MerchantID:   "cma6ptpdn0000f0lx2mp3lhob",
			Name:         "Marketing Agency",
			Email:        stringPtr("<EMAIL>"),
			Phone:        stringPtr("******-999-0000"),
			Address:      stringPtr("852 Marketing Blvd, Market City, MC 99999"),
			TaxID:        stringPtr("99-9999999"),
			PaymentTerms: stringPtr("Net 30"),
			IsActive:     true,
		},
		{
			MerchantID:   "cma6ptpdn0000f0lx2mp3lhob",
			Name:         "Legal Services",
			Email:        stringPtr("<EMAIL>"),
			Phone:        stringPtr("******-000-1111"),
			Address:      stringPtr("963 Legal Lane, Legal City, LC 00000"),
			TaxID:        stringPtr("00-0000000"),
			PaymentTerms: stringPtr("Net 15"),
			IsActive:     true,
		},
	}

	for _, vendor := range vendors {
		if err := db.Create(&vendor).Error; err != nil {
			log.Fatalf("Failed to create vendor %s: %v", vendor.Name, err)
		}
		log.Printf("Created vendor: %s", vendor.Name)
	}

	// Create chart of accounts
	accounts := []models.ChartOfAccount{
		{
			MerchantID:  "cma6ptpdn0000f0lx2mp3lhob",
			Code:        "1000",
			Name:        "Cash",
			Type:        models.AccountTypeAsset,
			Description: stringPtr("Cash and cash equivalents"),
			IsActive:    true,
			Balance:     decimal.NewFromFloat(50000.00),
		},
		{
			MerchantID:  "cma6ptpdn0000f0lx2mp3lhob",
			Code:        "1200",
			Name:        "Accounts Receivable",
			Type:        models.AccountTypeAsset,
			Description: stringPtr("Money owed by customers"),
			IsActive:    true,
			Balance:     decimal.NewFromFloat(29000.00),
		},
		{
			MerchantID:  "cma6ptpdn0000f0lx2mp3lhob",
			Code:        "2000",
			Name:        "Accounts Payable",
			Type:        models.AccountTypeLiability,
			Description: stringPtr("Money owed to vendors"),
			IsActive:    true,
			Balance:     decimal.NewFromFloat(20500.00),
		},
		{
			MerchantID:  "cma6ptpdn0000f0lx2mp3lhob",
			Code:        "4000",
			Name:        "Sales Revenue",
			Type:        models.AccountTypeRevenue,
			Description: stringPtr("Revenue from sales"),
			IsActive:    true,
			Balance:     decimal.NewFromFloat(0.00),
		},
		{
			MerchantID:  "cma6ptpdn0000f0lx2mp3lhob",
			Code:        "5000",
			Name:        "Cost of Goods Sold",
			Type:        models.AccountTypeExpense,
			Description: stringPtr("Direct costs of goods sold"),
			IsActive:    true,
			Balance:     decimal.NewFromFloat(0.00),
		},
		{
			MerchantID:  "cma6ptpdn0000f0lx2mp3lhob",
			Code:        "6000",
			Name:        "Operating Expenses",
			Type:        models.AccountTypeExpense,
			Description: stringPtr("General operating expenses"),
			IsActive:    true,
			Balance:     decimal.NewFromFloat(0.00),
		},
	}

	for _, account := range accounts {
		if err := db.Create(&account).Error; err != nil {
			log.Fatalf("Failed to create account %s: %v", account.Name, err)
		}
		log.Printf("Created account: %s", account.Name)
	}

	log.Println("Database seeding completed successfully!")
}
